#2025.06.18 撤回受理功能 未执行
alter table public.biz_wage_complaint_stats
    add processor_user bigint;

comment on column public.biz_wage_complaint_stats.processor_user is '处理人';
UPDATE biz_wage_complaint_stats
SET processor_user=create_user
WHERE processor_user IS NULL
alter table public.biz_wage_complaint_stats
    alter column processor_user set not null;
comment on column public.biz_wage_complaint.complaint_status is '投诉状态1已投诉2已受理3已完成4已撤诉5驳回';
comment on column public.biz_wage_complaint_stats.complaint_stats is '处理状态1已投诉2已受理3已完成4已撤诉5驳回';
alter table public.biz_wage_complaint
    add remark varchar(255);

comment on column public.biz_wage_complaint.remark is '拒绝原因';

alter table public.biz_wage_complaint
    add sort integer;

comment on column public.biz_wage_complaint.sort is '投诉顺序';

WITH ranked_complaints AS (SELECT id,
                                  ROW_NUMBER() OVER (
                                      PARTITION BY complaint_person_id_card
                                      ORDER BY create_time ASC
                                      ) as new_sort
                           FROM biz_wage_complaint
                           WHERE complaint_type = 1
                             AND sort IS NULL
                             AND is_deleted = 0)
UPDATE biz_wage_complaint
SET sort = ranked_complaints.new_sort FROM ranked_complaints
WHERE biz_wage_complaint.id = ranked_complaints.id;

DROP TABLE IF EXISTS biz_agent_ocr_info;
CREATE TABLE biz_agent_ocr_info
(
    id            bigint       NOT NULL,
    ocr_id_card   VARCHAR(60)  NOT NULL,
    ocr_name      VARCHAR(255) NOT NULL,
    ocr_address   VARCHAR(255) NOT NULL,
    ocr_nation    VARCHAR(255) NOT NULL,
    save_ocr_info VARCHAR(255) NOT NULL,
    tenant_id     VARCHAR(12) DEFAULT 000000,
    create_user   bigint,
    create_dept   bigint,
    create_time   TIMESTAMP,
    update_user   bigint,
    update_time   TIMESTAMP,
    status        INTEGER     DEFAULT 1,
    is_deleted    INTEGER     DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT ON TABLE biz_agent_ocr_info IS '代理人OCR信息';
COMMENT ON COLUMN biz_agent_ocr_info.id IS '主键';
COMMENT ON COLUMN biz_agent_ocr_info.ocr_id_card IS 'OCR识别的身份证号';
COMMENT ON COLUMN biz_agent_ocr_info.ocr_name IS 'OCR识别的姓名';
COMMENT ON COLUMN biz_agent_ocr_info.ocr_address IS 'OCR识别的地址';
COMMENT ON COLUMN biz_agent_ocr_info.ocr_nation IS 'OCR识别的民族';
COMMENT ON COLUMN biz_agent_ocr_info.save_ocr_info IS '保存的OCR信息';
COMMENT ON COLUMN biz_agent_ocr_info.tenant_id IS '租户id';
COMMENT ON COLUMN biz_agent_ocr_info.create_user IS '创建人';
COMMENT ON COLUMN biz_agent_ocr_info.create_dept IS '创建部门';
COMMENT ON COLUMN biz_agent_ocr_info.create_time IS '创建时间';
COMMENT ON COLUMN biz_agent_ocr_info.update_user IS '修改人';
COMMENT ON COLUMN biz_agent_ocr_info.update_time IS '修改时间';
COMMENT ON COLUMN biz_agent_ocr_info.status IS '状态';
COMMENT ON COLUMN biz_agent_ocr_info.is_deleted IS '是否已删除';

#2025.06.17 现场扫码 已更新
alter table public.biz_wage_complaint
    add is_on_site integer default 0 not null;

comment on column public.biz_wage_complaint.is_on_site is '是否现场扫码';
