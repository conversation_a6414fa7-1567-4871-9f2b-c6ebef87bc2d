#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      ##将docker脚本部署的redis服务映射为宿主机ip
      ##生产环境推荐使用阿里云高可用redis服务并设置密码
      host: ***********
      port: 3379
      password: Anyu@312199
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # PostgreSQL
    url: ******************************************
    username: anyu
    password: Anyu@312199

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:2888

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: true
  #oss服务地址
  endpoint: http://***********:9000
  #oss转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  #transform-endpoint: http://***********:9000
  #访问key
  access-key: bladexadmin
  #密钥key
  secret-key: bladexadmin
  #存储桶
  bucket-name: bladex

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: true
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://***********:3379
    password: Anyu@312199
#妈心理测试微信小程序配置
wx:
  miniapp:
    configs:
      - appId: wx0fff35248278a350
        secret: acd6c4a00efe0d030fe094798b1f2e0b
#腾讯云配置
tencent:
  secretId: AKIDMdvCc0R2Wq23uIYdXGoDSWVL4c4OXVJw
  secretKey: UlDhBrRr9HEGnKwcAZqLqEodJyU0PdgY
