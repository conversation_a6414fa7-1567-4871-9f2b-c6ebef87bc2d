#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: **************
      port: 3379
      password:
      database: 8
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
    #url: *********************************************************************************************************************************************************************************************************************************************
    #username: root
    #password: root
    # PostgreSQL
    url: *****************************************
    username: zmz
    password: xczv1213
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:2888

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: true
  #oss服务地址
  endpoint: http://127.0.0.1:9000
  #oss转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: http://localhost:9000
  #访问key
  access-key: bladexadmin
  #密钥key
  secret-key: bladexadmin
  #存储桶
  bucket-name: bladex

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: true
    ##redis服务地址
    address: redis://**************:3379
#妈心理测试微信小程序配置
wx:
  miniapp:
    configs:
      - appId: wx0fff35248278a350
        secret: acd6c4a00efe0d030fe094798b1f2e0b
#腾讯云配置
tencent:
  secretId: AKIDMdvCc0R2Wq23uIYdXGoDSWVL4c4OXVJw
  secretKey: UlDhBrRr9HEGnKwcAZqLqEodJyU0PdgY
