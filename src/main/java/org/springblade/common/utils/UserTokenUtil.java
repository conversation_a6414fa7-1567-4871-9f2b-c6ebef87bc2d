package org.springblade.common.utils;

import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;

public class UserTokenUtil {

	/**
	 * 通过Token获取身份证号
	 *
	 * @return String
	 */
	public static String getIdCard() {
		String idCard = AuthUtil.getUser().getDetail().getStr("ext");
		if (Func.isBlank(idCard)) {
			throw new ServiceException("用户未进行实名认证");
		}
		return idCard;
	}


	/**
	 * 通过Token获取姓名
	 *
	 * @return String
	 */
	public static String getRealName() {
		String realName = AuthUtil.getUser().getDetail().getStr("realName");
		if (Func.isBlank(realName)) {
			throw new ServiceException("用户未进行实名认证");
		}
		return realName;
	}

	/**
	 * 通过Token获取姓名
	 *
	 * @return String
	 */
	public static String getPhone() {
		String realName = AuthUtil.getUser().getDetail().getStr("phone");
		if (Func.isBlank(realName)) {
			throw new ServiceException("用户未绑定手机号");
		}
		return realName;
	}

}
