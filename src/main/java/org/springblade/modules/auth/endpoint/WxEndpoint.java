package org.springblade.modules.auth.endpoint;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.auth.config.WxMaProperties;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * WxEndpoint
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
public class WxEndpoint {

	private final WxMaProperties wxMaProperties;
	private final WxMaService wxMaService;
	private final IUserService userService;

	/**
	 * 验证手机号
	 */
	@SneakyThrows
	@PostMapping("/oauth/wx/validate-phone-no")
	public R<Boolean> validatePhoneNo(@RequestParam String code) {
		if (Func.isEmpty(AuthUtil.getUser())) {
			throw new SecureException("用户登录失效");
		}
		//按目前系统需求不需要支持多小程序，取get0
		String appid = wxMaProperties.getConfigs().get(0).getAppid();
		if (!wxMaService.switchover(appid)) {
			throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
		}
		WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNumber(code);
		WxMaConfigHolder.remove();

		String phone = phoneNoInfo.getPhoneNumber();
		if (Func.isBlank(phone)) {
			throw new ServiceException("未获取到手机号");
		}

		User user = new User();
		user.setPhone(phone);
		return R.status(userService.updateUserInfo(user));
	}

}
