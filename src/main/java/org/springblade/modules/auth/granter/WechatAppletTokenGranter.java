package org.springblade.modules.auth.granter;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springblade.core.oauth2.exception.GranterInvalidException;
import org.springblade.core.oauth2.granter.AbstractTokenGranter;
import org.springblade.core.oauth2.handler.PasswordHandler;
import org.springblade.core.oauth2.provider.OAuth2Request;
import org.springblade.core.oauth2.service.OAuth2ClientService;
import org.springblade.core.oauth2.service.OAuth2User;
import org.springblade.core.oauth2.service.OAuth2UserService;
import org.springblade.modules.auth.config.WxMaProperties;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.modules.system.pojo.entity.UserInfo;
import org.springblade.modules.system.pojo.entity.UserOauth;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatAppletTokenGranter extends AbstractTokenGranter {

	private final IUserService userService;
	private final WxMaProperties wxMaProperties;
	private final WxMaService wxMaService;

	public WechatAppletTokenGranter(OAuth2ClientService clientService, OAuth2UserService oAuth2UserService, PasswordHandler passwordHandler,
									IUserService userService, WxMaProperties wxMaProperties, WxMaService wxMaService) {
		super(clientService, oAuth2UserService, passwordHandler);
		this.userService = userService;
		this.wxMaProperties = wxMaProperties;
		this.wxMaService = wxMaService;
	}

	@Override
	public String type() {
		return WECHAT_APPLET;
	}

	@Override
	public OAuth2User user(OAuth2Request request) {
		//按目前系统需求不需要支持多小程序，取get0
		String appid = wxMaProperties.getConfigs().get(0).getAppid();
		if (!wxMaService.switchover(appid)) {
			throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
		}

		String code = request.getCode();
		String tenantId = request.getTenantId();

		try {
			WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
			String openId = session.getOpenid();
			String sessionKey = session.getSessionKey();
			log.info("{}获取到的sessionKey是:{}", code, sessionKey);
			log.info("{}获取到的openId是:{}", code, openId);

			// 组装数据
			UserOauth userOauth = new UserOauth();
			userOauth.setSource(WECHAT_APPLET);
			userOauth.setTenantId(tenantId);
			userOauth.setUuid(openId);
			userOauth.setUsername(openId);
			UserInfo userInfo = userService.userInfo(userOauth);

			// 设置Oauth2用户信息
			OAuth2User user = TokenUtil.convertUser(userInfo, request);
			// 设置客户端信息
			user.setClient(client(request));
			return user;

		} catch (WxErrorException e) {
			log.error("{}登录失败，异常原因为:{}", code, e.getMessage());
			throw new GranterInvalidException(e.getMessage());
		} finally {
			WxMaConfigHolder.remove();
		}
	}
}
