package org.springblade.modules.biz.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.biz.config.TencentConfig;
import org.springblade.modules.biz.utils.OcrUtil;
import org.springblade.modules.system.pojo.entity.UserApp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 后台获取Ocr识别情况
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_NAME_PREFIX + "biz/ocr")
public class OcrController {

	private final TencentConfig config;

	/**
	 * OCR识别接口
	 */
	@PostMapping("/ocr")
	public R<Object> ocr(@RequestBody Map<String, String> map) {
		String images = map.get("img");
		if (Func.isBlank(images)) {
			throw new ServiceException("请检查图片信息是否传入");
		}

		try {
			// 调用腾讯云OCR服务
			IDCardOCRResponse resp = OcrUtil.callTencentOcrService(images, config);

			// 保存OCR识别结果
			saveOcrResult(images, resp);

			return R.data(resp);
		} catch (TencentCloudSDKException e) {
			log.error("调用腾讯云OCR服务失败", e);
			return R.fail("OCR识别失败: " + e.getMessage());
		} catch (ServiceException e) {
			log.error("业务处理异常", e);
			return R.fail(e.getMessage());
		} catch (Exception e) {
			log.error("系统异常", e);
			return R.fail("系统异常: " + e.getMessage());
		}
	}

	// 保存OCR识别结果的方法
	private void saveOcrResult(String images, IDCardOCRResponse resp) {
		String fileName = OcrUtil.buildOcrFileName(resp.getIdNum(), "idcard");
		BladeFile file = OcrUtil.saveOcrImageToOss(images, fileName, "idcard");

		// 更新用户OCR信息
		updateUserOcrInfo(file);
	}

	// 更新用户OCR信息的方法
	private void updateUserOcrInfo(BladeFile file) {
		UserApp old = Db.getOne(Wrappers.lambdaQuery(UserApp.class)
			.eq(UserApp::getUserId, AuthUtil.getUserId()));

		if (Func.isEmpty(old)) {
			old = new UserApp();
			old.setUserId(AuthUtil.getUserId());
		}

		old.setOcrIdcard(file.getName());
		old.insertOrUpdate();
	}

}
