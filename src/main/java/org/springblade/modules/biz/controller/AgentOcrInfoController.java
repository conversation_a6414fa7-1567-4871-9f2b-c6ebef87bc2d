package org.springblade.modules.biz.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity;
import org.springblade.modules.biz.pojo.vo.AgentOcrInfoVO;
import org.springblade.modules.biz.excel.AgentOcrInfoExcel;
import org.springblade.modules.biz.wrapper.AgentOcrInfoWrapper;
import org.springblade.modules.biz.service.IAgentOcrInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.modules.biz.config.TencentConfig;
import org.springblade.modules.biz.utils.OcrUtil;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 代理人OCR信息 控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_NAME_PREFIX + "biz/agentOcrInfo")
@Tag(name = "代理人OCR信息", description = "代理人OCR信息接口")
public class AgentOcrInfoController extends BladeController {

	private final IAgentOcrInfoService agentOcrInfoService;
	private final TencentConfig tencentConfig;

	/**
	 * 代理人OCR识别接口
	 */
	@PostMapping("/ocr")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "代理人OCR识别", description = "传入图片base64进行OCR识别")
	public R<Object> agentOcr(@RequestBody Map<String, String> map) {
		String images = map.get("img");
		if (Func.isBlank(images)) {
			throw new ServiceException("请检查图片信息是否传入");
		}

		try {
			IDCardOCRResponse resp = OcrUtil.callTencentOcrService(images, tencentConfig);

			AgentOcrInfoEntity agentOcrInfo = saveAgentOcrResult(images, resp);
			return R.data(agentOcrInfo);
		} catch (TencentCloudSDKException e) {
			log.error("调用腾讯云OCR服务失败", e);
			return R.fail("OCR识别失败: " + e.getMessage());
		} catch (ServiceException e) {
			log.error("业务处理异常", e);
			return R.fail(e.getMessage());
		} catch (Exception e) {
			log.error("系统异常", e);
			return R.fail("系统异常: " + e.getMessage());
		}
	}

	/**
	 * 代理人OCR信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "详情", description = "传入agentOcrInfo")
	public R<AgentOcrInfoVO> detail(AgentOcrInfoEntity agentOcrInfo) {
		AgentOcrInfoEntity detail = agentOcrInfoService.getOne(Condition.getQueryWrapper(agentOcrInfo));
		return R.data(AgentOcrInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 工资投诉表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入agentOcrInfo")
	public R<IPage<AgentOcrInfoVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> agentOcrInfo, Query query) {
		IPage<AgentOcrInfoEntity> pages = agentOcrInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(agentOcrInfo, AgentOcrInfoEntity.class));
		return R.data(AgentOcrInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 工资投诉表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入agentOcrInfo")
	public R<IPage<AgentOcrInfoVO>> page(AgentOcrInfoVO agentOcrInfo, Query query) {
		IPage<AgentOcrInfoVO> pages = agentOcrInfoService.selectAgentOcrInfoPage(Condition.getPage(query), agentOcrInfo);
		return R.data(pages);
	}

	/**
	 * 工资投诉表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入agentOcrInfo")
	public R save(@Valid @RequestBody AgentOcrInfoEntity agentOcrInfo) {
		return R.status(agentOcrInfoService.save(agentOcrInfo));
	}

	/**
	 * 工资投诉表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入agentOcrInfo")
	public R update(@Valid @RequestBody AgentOcrInfoEntity agentOcrInfo) {
		return R.status(agentOcrInfoService.updateById(agentOcrInfo));
	}

	/**
	 * 工资投诉表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入agentOcrInfo")
	public R submit(@Valid @RequestBody AgentOcrInfoEntity agentOcrInfo) {
		return R.status(agentOcrInfoService.saveOrUpdate(agentOcrInfo));
	}

	/**
	 * 工资投诉表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(agentOcrInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-agentOcrInfo")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description = "传入agentOcrInfo")
	public void exportAgentOcrInfo(@Parameter(hidden = true) @RequestParam Map<String, Object> agentOcrInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<AgentOcrInfoEntity> queryWrapper = Condition.getQueryWrapper(agentOcrInfo, AgentOcrInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(AgentOcrInfo::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(AgentOcrInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<AgentOcrInfoExcel> list = agentOcrInfoService.exportAgentOcrInfo(queryWrapper);
		ExcelUtil.export(response, "代理人OCR信息数据" + DateUtil.time(), "代理人OCR信息数据表", list, AgentOcrInfoExcel.class);
	}

	/**
	 * 保存代理人OCR识别结果
	 *
	 * @param images 图片base64字符串
	 * @param resp   OCR识别响应
	 * @return 保存的代理人OCR信息
	 */
	private AgentOcrInfoEntity saveAgentOcrResult(String images, IDCardOCRResponse resp) {
		// 保存图片到OSS
		String fileName = OcrUtil.buildOcrFileName(resp.getIdNum(), "agent");
		BladeFile file = OcrUtil.saveOcrImageToOss(images, fileName, "idcard");

		// 创建代理人OCR信息实体
		AgentOcrInfoEntity agentOcrInfo = new AgentOcrInfoEntity();
		agentOcrInfo.setOcrIdCard(resp.getIdNum());
		agentOcrInfo.setOcrName(resp.getName());
		agentOcrInfo.setOcrAddress(resp.getAddress());
		agentOcrInfo.setOcrNation(resp.getNation());
		agentOcrInfo.setSaveOcrInfo(file.getName());

		// 保存到数据库
		agentOcrInfoService.save(agentOcrInfo);

		return agentOcrInfo;
	}

}
