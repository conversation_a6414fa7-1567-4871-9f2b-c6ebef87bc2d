package org.springblade.modules.biz.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.utils.UserTokenUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.biz.excel.WageComplaintExcel;
import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;
import org.springblade.modules.biz.service.IWageComplaintStatsService;
import org.springblade.modules.resource.utils.SmsUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintVO;
import org.springblade.modules.biz.wrapper.WageComplaintWrapper;
import org.springblade.modules.biz.service.IWageComplaintService;
import org.springblade.core.boot.ctrl.BladeController;

import java.time.LocalDate;
import java.util.*;

/**
 * 工资投诉表 控制器
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-biz/wageComplaint")
@Tag(name = "工资投诉表", description = "工资投诉表接口")
public class WageComplaintController extends BladeController {

	private final IWageComplaintService wageComplaintService;
	private final IWageComplaintStatsService wageComplaintStatsService;
	private final BladeRedis bladeRedis;

	/**
	 * 工资投诉表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入wageComplaint")
	public R<WageComplaintVO> detail(WageComplaintEntity wageComplaint) {
		WageComplaintEntity detail = wageComplaintService.getOne(Condition.getQueryWrapper(wageComplaint));
		return R.data(WageComplaintWrapper.build().entityVO(detail));
	}

	/**
	 * 工资投诉表 详情
	 */
	@GetMapping("/get-app-detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入wageComplaint")
	public R<WageComplaintVO> detail(@RequestParam Long id) {
		WageComplaintEntity detail = wageComplaintService.getById(id);
		return R.data(WageComplaintWrapper.build().entityVO(detail));
	}

	/**
	 * 工资投诉表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入wageComplaint")
	public R<IPage<WageComplaintVO>> list(String createDateS, Query query) {
		LambdaQueryWrapper<WageComplaintEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(WageComplaintEntity.class)
			.orderByDesc(WageComplaintEntity::getCreateTime);
		if (Func.isNotBlank(createDateS)) {
			LocalDate createDate = LocalDate.parse(createDateS);
			lambdaQueryWrapper.eq(WageComplaintEntity::getCreateDate, createDate);
		}
		IPage<WageComplaintEntity> pages = wageComplaintService.page(Condition.getPage(query), lambdaQueryWrapper);
		return R.data(WageComplaintWrapper.build().pageVO(pages));
	}

	/**
	 * 设置处理状态
	 */
	@Transactional(rollbackFor = Exception.class)
	@PostMapping("/set-complaint-status")
	public R<Boolean> setComplaintStatus(@RequestParam Long id, @RequestParam Integer status, String remark) {
		WageComplaintEntity wageComplaintEntity = new WageComplaintEntity();
		wageComplaintEntity.setId(id);
		wageComplaintEntity.setComplaintStatus(status);
		wageComplaintEntity.setRemark(remark);
		return R.status(wageComplaintService.updateById(wageComplaintEntity) && wageComplaintStatsService.setWageComplaintStats(id, status));
	}


	/**
	 * 临时使用导出数据接口
	 *
	 * @param createDateS
	 * @param response
	 */
	@GetMapping("/export")
	public void exportUser(@RequestParam String createDateS, HttpServletResponse response) {
		LocalDate createDate = LocalDate.parse(createDateS);
		LambdaQueryWrapper<WageComplaintEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(WageComplaintEntity.class)
			.orderByAsc(WageComplaintEntity::getCreateTime).eq(WageComplaintEntity::getCreateDate, createDate);
		List<WageComplaintEntity> list = wageComplaintService.list(lambdaQueryWrapper);
		List<WageComplaintExcel> excelList = new ArrayList<>();
		list.forEach(w -> {
			WageComplaintVO vo = WageComplaintWrapper.build().entityVO(w);
			WageComplaintExcel excel = BeanUtil.copy(vo, WageComplaintExcel.class);
			excel.setComplaintTypeS(w.getComplaintType() == 1 ? "工程领域" : "非工程领域");
			excel.setComplaintChannel(w.getIsOnSite() == 1 ? "现场投诉" : w.getIsOnSite() == 2 ? "非现场投诉" : "未知投诉渠道");
			excel.setWorkLocationS(extractAddress(w.getWorkLocation()));
			if (Func.isNotEmpty(w.getArrearsPeriod())) {
				excel.setArrearsPeriodS("开始时间：" + w.getArrearsPeriod().getString(0) + "  结束时间：" + w.getArrearsPeriod().getString(1));
			}
			excelList.add(excel);
		});
		ExcelUtil.export(response, "用户数据" + DateUtil.time(), "用户数据表", excelList, WageComplaintExcel.class);
	}

	private String extractAddress(JSONArray workLocation) {
		if (workLocation == null || workLocation.isEmpty()) {
			return "地址信息不存在";
		}

		try {
			// 获取第一个地址对象
			JSONObject addressObj = workLocation.getJSONObject(0);

			// 提取地址名称和详情
			String name = addressObj.getString("name");
			String address = addressObj.getString("address");

			// 构建格式化的地址字符串
			return "地址名称：" + name + "，地址详情：" + address;
		} catch (Exception e) {
			// 处理异常情况
			return "地址信息解析失败：" + e.getMessage();
		}
	}

	/**
	 * 工资投诉表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入wageComplaint")
	public R<IPage<WageComplaintVO>> page(WageComplaintVO wageComplaint, Query query) {
		IPage<WageComplaintVO> pages = wageComplaintService.selectWageComplaintPage(Condition.getPage(query), wageComplaint);
		return R.data(pages);
	}

	/**
	 * 工资投诉表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入wageComplaint")
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> save(@Valid @RequestBody WageComplaintVO wageComplaint, BindingResult bindingResult) {

		if (bindingResult.hasErrors()) {
			throw new ServiceException("请检查必填项");
		}

		String key = "COMPLAINT:SAVE:" + AuthUtil.getUserId();
		Object o = bladeRedis.get(key);
		if (Func.isNotEmpty(o)) {
			throw new ServiceException("请勿频繁提交投诉，一段时间只能提交一次");
		}
		bladeRedis.setEx(key, AuthUtil.getUserId(), 60L);

		if (!checkAddressPrefix(wageComplaint.getWorkLocation())) {
			throw new ServiceException("投诉所在地需为定州市");
		}

		String idCard = UserTokenUtil.getIdCard();
		if (wageComplaint.getComplaintType() == 1) {
			int complaintSequence = wageComplaintService.getEngineeringComplaintSequence(idCard);
			wageComplaint.setSort(complaintSequence);
		}

		// 校验委托人信息
		JSONArray entrustedPerson = wageComplaint.getEntrustedPerson();
		if (entrustedPerson != null && !entrustedPerson.isEmpty()) {
			for (int i = 0; i < entrustedPerson.size(); i++) {
				JSONObject person = entrustedPerson.getJSONObject(i);
				// 校验手机号
				String phone = person.getString("phone");
				if (phone != null && !cn.hutool.core.util.PhoneUtil.isMobile(phone)) {
					throw new ServiceException("委托人手机号格式不正确");
				}
				// 校验身份证号
				String userExt = person.getString("userExt");
				if (userExt != null && !cn.hutool.core.util.IdcardUtil.isValidCard(userExt)) {
					throw new ServiceException("委托人身份证号格式不正确");
				}
			}
		}
		wageComplaint.setIsOnSite(parseIsOnSite(wageComplaint.getQrCodeScene()));
		wageComplaint.setComplaintPersonIdCard(UserTokenUtil.getIdCard());
		wageComplaint.setComplaintPersonName(UserTokenUtil.getRealName());
		wageComplaint.setComplaintStatus(1);
		boolean b = wageComplaintService.save(wageComplaint);

		WageComplaintStatsEntity wageComplaintStatsEntity = new WageComplaintStatsEntity();
		wageComplaintStatsEntity.setWageComplainId(wageComplaint.getId());
		wageComplaintStatsEntity.setComplaintStats(1);
		wageComplaintStatsEntity.setSort(1);
		wageComplaintStatsEntity.setProcessorUser(AuthUtil.getUserId());
		boolean c = wageComplaintStatsService.save(wageComplaintStatsEntity);

		String phone = UserTokenUtil.getPhone();
		Map<String, String> map = new HashMap<>();
//		map.put("code", wageComplaint.getComplaintCompanyName());
		SmsUtil.sendMessage("bindPhone", map, phone);

		return R.status(b && c);
	}

	public static boolean checkAddressPrefix(JSONArray workLocations) {
		if (workLocations == null || workLocations.isEmpty()) {
			return false;
		}

		for (int i = 0; i < workLocations.size(); i++) {
			try {
				JSONObject location = workLocations.getJSONObject(i);
				String address = location.getString("address");

				if (address == null || !address.startsWith("河北省保定市定州市")) {
					return false;
				}
			} catch (Exception e) {
				return false;
			}
		}

		return true;
	}

	/**
	 * 解析场景参数并获取isOnSite的值
	 *
	 * @param scene 前台传入的场景参数（例如："isOnSite=1&dsad=1"）
	 * @return 如果存在isOnSite且值为1，返回1；否则返回2
	 * @throws IllegalArgumentException 当参数格式不符合要求时抛出异常
	 */
	public static int parseIsOnSite(String scene) throws IllegalArgumentException {
		if (scene == null) {
			return 2; // 允许null，返回2
		}

		if (scene.isEmpty()) {
			return 2; // 空字符串返回2
		}

		// 解码并分割参数
		Map<String, String> params = new HashMap<>();
		String[] paramPairs = scene.split("&");

		for (String pair : paramPairs) {
			if (pair.isEmpty()) {
				throw new IllegalArgumentException("Invalid parameter format: empty pair");
			}

			String[] keyValue = pair.split("=", 2);
			if (keyValue.length != 2) {
				throw new IllegalArgumentException("Invalid parameter format: " + pair);
			}

			params.put(keyValue[0], keyValue[1]);
		}

		// 检查isOnSite参数
		return params.containsKey("isOnSite") && "1".equals(params.get("isOnSite")) ? 1 : 2;
	}

	/**
	 * 工资投诉表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入wageComplaint")
	public R update(@Valid @RequestBody WageComplaintEntity wageComplaint) {
		return R.status(wageComplaintService.updateById(wageComplaint));
	}

	/**
	 * 工资投诉表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入wageComplaint")
	public R submit(@Valid @RequestBody WageComplaintEntity wageComplaint) {
		return R.status(wageComplaintService.saveOrUpdate(wageComplaint));
	}

	/**
	 * 工资投诉表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(wageComplaintService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 获取历史投诉案件列表
	 */
	@GetMapping("/get-home-list")
	public R<List<WageComplaintVO>> getHomeList() {
		String idCard = UserTokenUtil.getIdCard();
		return R.data(wageComplaintService.getHistoryComplaintList(idCard));
	}

}
