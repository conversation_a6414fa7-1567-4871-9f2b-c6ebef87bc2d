package org.springblade.modules.biz.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity;
import org.springblade.modules.biz.pojo.vo.AgentOcrInfoVO;

import java.util.Objects;

/**
 * 工资投诉表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public class AgentOcrInfoWrapper extends BaseEntityWrapper<AgentOcrInfoEntity, AgentOcrInfoVO> {

	public static AgentOcrInfoWrapper build() {
		return new AgentOcrInfoWrapper();
	}

	@Override
	public AgentOcrInfoVO entityVO(AgentOcrInfoEntity agentOcrInfo) {
		AgentOcrInfoVO agentOcrInfoVO = Objects.requireNonNull(BeanUtil.copyProperties(agentOcrInfo, AgentOcrInfoVO.class));

		//User createUser = UserCache.getUser(agentOcrInfo.getCreateUser());
		//User updateUser = UserCache.getUser(agentOcrInfo.getUpdateUser());
		//agentOcrInfoVO.setCreateUserName(createUser.getName());
		//agentOcrInfoVO.setUpdateUserName(updateUser.getName());

		return agentOcrInfoVO;
	}


}
