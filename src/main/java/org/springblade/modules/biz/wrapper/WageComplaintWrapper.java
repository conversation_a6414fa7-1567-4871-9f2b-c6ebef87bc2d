package org.springblade.modules.biz.wrapper;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintVO;
import org.springblade.modules.biz.service.IWageComplaintStatsService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * 工资投诉表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public class WageComplaintWrapper extends BaseEntityWrapper<WageComplaintEntity, WageComplaintVO> {

	public static WageComplaintWrapper build() {
		return new WageComplaintWrapper();
	}

	@Override
	public WageComplaintVO entityVO(WageComplaintEntity wageComplaint) {
		WageComplaintVO wageComplaintVO = Objects.requireNonNull(BeanUtil.copyProperties(wageComplaint, WageComplaintVO.class));

		IWageComplaintStatsService wageComplaintStatsService = SpringUtil.getBean(IWageComplaintStatsService.class);
		wageComplaintVO.setStatsEntities(wageComplaintStatsService.list(Wrappers.lambdaQuery(WageComplaintStatsEntity.class)
			.eq(WageComplaintStatsEntity::getWageComplainId, wageComplaint.getId()).orderByAsc(WageComplaintStatsEntity::getSort)));

		IUserService userService = SpringUtil.getBean(IUserService.class);
		User user = userService.getById(wageComplaintVO.getCreateUser());
		if (Func.isEmpty(user)) {
			throw new ServiceException("未获取到用户信息，请检查数据是否异常");
		}
		wageComplaintVO.setComplaintPersonPhone(user.getPhone());
		if (Func.isNotBlank(wageComplaint.getComplaintPersonIdCard())) {
			wageComplaintVO.setAge(IdcardUtil.getAgeByIdCard(wageComplaint.getComplaintPersonIdCard()));
			wageComplaintVO.setSex(IdcardUtil.getGenderByIdCard(wageComplaint.getComplaintPersonIdCard()));
		}

		// 构建投诉描述字段
		wageComplaintVO.setComplaintDescription(buildComplaintDescription(wageComplaint));

		//User createUser = UserCache.getUser(wageComplaint.getCreateUser());
		//User updateUser = UserCache.getUser(wageComplaint.getUpdateUser());
		//wageComplaintVO.setCreateUserName(createUser.getName());
		//wageComplaintVO.setUpdateUserName(updateUser.getName());

		return wageComplaintVO;
	}

	/**
	 * 构建投诉描述
	 * 格式：投诉时间[xxxx年xx月xx日]，[投诉人姓名]投诉[xxxx年xx月xx日]至[xxxx年xx月xx日]在[务工单位]，从事[工种]，被拖欠工资[金额]元
	 *
	 * @param wageComplaint 投诉实体
	 * @return 投诉描述字符串
	 */
	private String buildComplaintDescription(WageComplaintEntity wageComplaint) {
		StringBuilder description = new StringBuilder();

		// 投诉时间
		String complaintTime = formatComplaintTime(wageComplaint.getCreateTime());
		description.append(complaintTime).append("，");

		// 投诉人姓名
		String complaintPersonName = Func.isNotBlank(wageComplaint.getComplaintPersonName())
			? wageComplaint.getComplaintPersonName() : "未知";
		description.append(complaintPersonName).append("投诉");

		// 欠薪时段
		String arrearsPeriodStr = formatArrearsPeriod(wageComplaint.getArrearsPeriod());
		description.append(arrearsPeriodStr);

		// 务工单位
		String companyName = Func.isNotBlank(wageComplaint.getComplaintCompanyName())
			? wageComplaint.getComplaintCompanyName() : "未知单位";
		description.append("在").append(companyName);

		// 工种
		String workType = Func.isNotBlank(wageComplaint.getWorkType())
			? wageComplaint.getWorkType() : "其他工种";
		description.append("，从事").append(workType);

		// 拖欠金额
		BigDecimal arrearsAmount = wageComplaint.getArrearsAmount();
		String amountStr = (arrearsAmount != null) ? arrearsAmount.toString() : "0";
		description.append("，被拖欠工资").append(amountStr).append("元");

		return description.toString();
	}

	/**
	 * 格式化投诉时间
	 *
	 * @param createTime 创建时间
	 * @return 格式化后的投诉时间字符串
	 */
	private String formatComplaintTime(Date createTime) {
		if (createTime == null) {
			return "未知时间";
		}

		try {
			java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyy年MM月dd日");
			return formatter.format(createTime);
		} catch (Exception e) {
			return "未知时间";
		}
	}

	/**
	 * 格式化欠薪时段
	 *
	 * @param arrearsPeriod 欠薪时段JSON数组
	 * @return 格式化后的时段字符串
	 */
	private String formatArrearsPeriod(JSONArray arrearsPeriod) {
		if (arrearsPeriod == null || arrearsPeriod.isEmpty()) {
			return "未知时间段";
		}

		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

			if (arrearsPeriod.size() >= 2) {
				String startDateStr = arrearsPeriod.getString(0);
				String endDateStr = arrearsPeriod.getString(1);

				if (Func.isNotBlank(startDateStr) && Func.isNotBlank(endDateStr)) {
					LocalDate startDate = LocalDate.parse(startDateStr);
					LocalDate endDate = LocalDate.parse(endDateStr);

					return startDate.format(formatter) + "至" + endDate.format(formatter);
				}
			}

			// 如果只有一个日期或格式不正确，返回默认值
			return "未知时间段";
		} catch (Exception e) {
			return "未知时间段";
		}
	}

}
