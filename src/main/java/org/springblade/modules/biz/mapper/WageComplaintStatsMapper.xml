<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.biz.mapper.WageComplaintStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wageComplaintStatsResultMap" type="org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity">
        <result column="id" property="id"/>
        <result column="wage_complain_id" property="wageComplainId"/>
        <result column="sort" property="sort"/>
        <result column="complaint_stats" property="complaintStats"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectWageComplaintStatsPage" resultMap="wageComplaintStatsResultMap">
        select *
        from biz_wage_complaint_stats
        where is_deleted = 0
    </select>


    <select id="exportWageComplaintStats" resultType="org.springblade.modules.biz.excel.WageComplaintStatsExcel">
        SELECT *
        FROM biz_wage_complaint_stats ${ew.customSqlSegment}
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">

        SELECT MAX(sort)
        FROM biz_wage_complaint_stats
        WHERE is_deleted = 0
          AND wage_complain_id = #{complainId}
    </select>

</mapper>
