<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.biz.mapper.WageComplaintMapper">

    <select id="selectWageComplaintPage">
        select *
        from biz_wage_complaint
        where is_deleted = 0
    </select>

    <select id="getEngineeringComplaintCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM biz_wage_complaint
        WHERE is_deleted = 0
          AND complaint_person_id_card = #{idCard}
          AND complaint_type = 1
    </select>
</mapper>
