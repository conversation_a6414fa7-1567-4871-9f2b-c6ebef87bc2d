package org.springblade.modules.biz.mapper;

import org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity;
import org.springblade.modules.biz.pojo.vo.AgentOcrInfoVO;
import org.springblade.modules.biz.excel.AgentOcrInfoExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资投诉表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface AgentOcrInfoMapper extends BaseMapper<AgentOcrInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page         分页参数
	 * @param agentOcrInfo 查询参数
	 * @return List<AgentOcrInfoVO>
	 */
	List<AgentOcrInfoVO> selectAgentOcrInfoPage(IPage page, AgentOcrInfoVO agentOcrInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<AgentOcrInfoExcel>
	 */
	List<AgentOcrInfoExcel> exportAgentOcrInfo(@Param("ew") Wrapper<AgentOcrInfoEntity> queryWrapper);

}
