package org.springblade.modules.biz.mapper;

import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资投诉表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface WageComplaintMapper extends BaseMapper<WageComplaintEntity> {

	/**
	 * 获取用户工程建设领域的投诉数量
	 *
	 * @param idCard 用户身份证号
	 * @return 投诉数量
	 */
	int getEngineeringComplaintCount(@Param("idCard") String idCard);

	/**
	 * 自定义分页
	 *
	 * @param page          分页参数
	 * @param wageComplaint 查询参数
	 * @return List<WageComplaintVO>
	 */
	List<WageComplaintVO> selectWageComplaintPage(IPage page, WageComplaintVO wageComplaint);

}
