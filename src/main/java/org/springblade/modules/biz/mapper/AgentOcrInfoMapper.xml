<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.biz.mapper.AgentOcrInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentOcrInfoResultMap" type="org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity">
        <result column="id" property="id"/>
        <result column="ocr_id_card" property="ocrIdCard"/>
        <result column="ocr_name" property="ocrName"/>
        <result column="ocr_address" property="ocrAddress"/>
        <result column="ocr_nation" property="ocrNation"/>
        <result column="save_ocr_info" property="saveOcrInfo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectAgentOcrInfoPage" resultMap="agentOcrInfoResultMap">
        select *
        from biz_agent_ocr_info
        where is_deleted = 0
    </select>


    <select id="exportAgentOcrInfo" resultType="org.springblade.modules.biz.excel.AgentOcrInfoExcel">
        SELECT *
        FROM biz_agent_ocr_info ${ew.customSqlSegment}
    </select>

</mapper>
