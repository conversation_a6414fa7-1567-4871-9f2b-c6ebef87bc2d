package org.springblade.modules.biz.mapper;

import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintStatsVO;
import org.springblade.modules.biz.excel.WageComplaintStatsExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资投诉表-copy Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface WageComplaintStatsMapper extends BaseMapper<WageComplaintStatsEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page               分页参数
	 * @param wageComplaintStats 查询参数
	 * @return List<WageComplaintStatsVO>
	 */
	List<WageComplaintStatsVO> selectWageComplaintStatsPage(IPage page, WageComplaintStatsVO wageComplaintStats);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<WageComplaintStatsExcel>
	 */
	List<WageComplaintStatsExcel> exportWageComplaintStats(@Param("ew") Wrapper<WageComplaintStatsEntity> queryWrapper);

	/**
	 * 获取最大顺序
	 *
	 * @param complainId 处理案件id
	 * @return
	 */
	Integer getMaxSort(Long complainId);

}
