package org.springblade.modules.biz.pojo.dto;

import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工资投诉表 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WageComplaintDTO extends WageComplaintEntity {
	@Serial
	private static final long serialVersionUID = 1L;

}
