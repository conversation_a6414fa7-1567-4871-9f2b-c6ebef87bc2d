package org.springblade.modules.biz.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;

/**
 * 工资投诉表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@TableName("biz_agent_ocr_info")
@Schema(description = "AgentOcrInfo对象")
@EqualsAndHashCode(callSuper = true)
public class AgentOcrInfoEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * OCR识别的身份证号
	 */
	@Schema(description = "OCR识别的身份证号")
	private String ocrIdCard;
	/**
	 * OCR识别的姓名
	 */
	@Schema(description = "OCR识别的姓名")
	private String ocrName;
	/**
	 * OCR识别的地址
	 */
	@Schema(description = "OCR识别的地址")
	private String ocrAddress;
	/**
	 * OCR识别的民族
	 */
	@Schema(description = "OCR识别的民族")
	private String ocrNation;
	/**
	 * 保存的OCR信息
	 */
	@Schema(description = "保存的OCR信息")
	private String saveOcrInfo;

}
