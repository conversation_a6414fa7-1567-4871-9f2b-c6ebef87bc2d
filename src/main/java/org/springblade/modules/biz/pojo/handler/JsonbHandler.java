package org.springblade.modules.biz.pojo.handler;

import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class J<PERSON>b<PERSON><PERSON><PERSON> extends Fastjson2TypeHandler {
	public JsonbHandler(Class<?> type) {
		super(type);
	}

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
		if (ps != null) {
			PGobject jsonObject = new PGobject();
			jsonObject.setType("jsonb");
			jsonObject.setValue(toJson(parameter));
			ps.setObject(i, jsonObject);
		}
	}

}
