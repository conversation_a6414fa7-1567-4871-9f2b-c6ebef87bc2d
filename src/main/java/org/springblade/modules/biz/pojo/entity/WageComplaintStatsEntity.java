package org.springblade.modules.biz.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;

/**
 * 工资投诉表-copy 实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("biz_wage_complaint_stats")
@Schema(description = "WageComplaintStats对象")
@EqualsAndHashCode(callSuper = true)
public class WageComplaintStatsEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 投诉的id
	 */
	@Schema(description = "投诉的id")
	private Long wageComplainId;
	/**
	 * 顺序
	 */
	@Schema(description = "顺序")
	private Integer sort;
	/**
	 * 处理状态
	 */
	@Schema(description = "处理状态1已投诉2已受理3已完成4已撤诉5驳回")
	private Integer complaintStats;

	/**
	 * 处理人信息
	 */
	private Long processorUser;

}
