package org.springblade.modules.biz.pojo.vo;

import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;

import java.io.Serial;
import java.util.List;

/**
 * 工资投诉表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WageComplaintVO extends WageComplaintEntity {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 状态信息
	 */
	private List<WageComplaintStatsEntity> statsEntities;

	/**
	 * 投诉人电话
	 */
	private String complaintPersonPhone;

	/**
	 * 前台传入的场景值
	 */
	private String qrCodeScene;

	/**
	 * 年龄
	 */
	private Integer age;

	/**
	 * 性别
	 */
	private Integer sex;

	/**
	 * 显示名称
	 * 工程领域：工程建设领域投诉案件1、2、3...
	 * 非工程领域：被投诉单位名称
	 */
	private String displayName;

	/**
	 * 投诉描述
	 */
	private String complaintDescription;

}
