package org.springblade.modules.biz.pojo.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.EqualsAndHashCode;
import org.hibernate.validator.group.GroupSequenceProvider;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.modules.biz.pojo.handler.JsonbHandler;
import org.springblade.modules.biz.validation.ValidationGroups;
import org.springblade.modules.biz.validation.WageComplaintGroupSequenceProvider;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 工资投诉表 实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName(value = "biz_wage_complaint", autoResultMap = true)
@Schema(description = "WageComplaint对象")
@EqualsAndHashCode(callSuper = true)
@GroupSequenceProvider(WageComplaintGroupSequenceProvider.class)
public class WageComplaintEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 被投诉项目名称
	 */
	@Schema(description = "被投诉项目名称")
	private String complaintProjectName;
	/**
	 * 被投诉单位名称
	 */
	@Schema(description = "被投诉单位名称")
	@NotBlank(groups = ValidationGroups.NonEngineeringGroup.class)
	private String complaintCompanyName;
	/**
	 * 单位地址
	 */
	@Schema(description = "单位地址")
	private String companyAddress;
	/**
	 * 建设单位地址
	 */
	@Schema(description = "建设单位地址")
	private String constructionCompanyAddress;
	/**
	 * 施工单位名称
	 */
	@Schema(description = "施工单位名称")
	private String constructionCompanyName;
	/**
	 * 总承包人
	 */
	@Schema(description = "总承包人")
	private String generalContractor;
	/**
	 * 总承包人联系电话
	 */
	@Schema(description = "总承包人联系电话")
	private String generalContractorPhone;
	/**
	 * 法人代表
	 */
	@Schema(description = "法人代表")
	private String legalRepresentative;
	/**
	 * 法人联系电话
	 */
	@Schema(description = "法人联系电话")
	private String legalRepresentativePhone;
	/**
	 * 项目经理/单位负责人
	 */
	@Schema(description = "项目经理/单位负责人")
	@NotBlank
	private String projectManager;
	/**
	 * 项目经理/单位负责人联系电话
	 */
	@Schema(description = "项目经理/单位负责人联系电话")
	@NotBlank
	private String projectManagerPhone;
	/**
	 * 投诉人姓名
	 */
	@Schema(description = "投诉人姓名")
	private String complaintPersonName;
	/**
	 * 投诉人身份证号
	 */
	@Schema(description = "投诉人身份证号")
	private String complaintPersonIdCard;
	/**
	 * 委托人
	 */
	@Schema(description = "委托人")
	@TableField(typeHandler = JsonbHandler.class)
	private JSONArray entrustedPerson;
	/**
	 * 务工所在地
	 */
	@Schema(description = "务工所在地")
	@NotEmpty
	@TableField(typeHandler = JsonbHandler.class)
	private JSONArray workLocation;
	/**
	 * 欠薪时段
	 */
	@Schema(description = "欠薪时段")
	@TableField(typeHandler = JsonbHandler.class)
	private JSONArray arrearsPeriod;
	/**
	 * 工种
	 */
	@Schema(description = "工种")
	private String workType;
	/**
	 * 欠薪人数
	 */
	@Schema(description = "欠薪人数")
	private Integer arrearsPeopleCount;
	/**
	 * 欠薪金额
	 */
	@Schema(description = "欠薪金额")
	@NotNull
	private BigDecimal arrearsAmount;
	/**
	 * 投诉内容
	 */
	@Schema(description = "投诉内容")
	@NotBlank
	private String complaintContent;
	/**
	 * 证据上传
	 */
	@Schema(description = "证据上传")
	@TableField(typeHandler = JsonbHandler.class)
	private JSONArray evidenceFiles;
	/**
	 * 投诉类型1工程领域2非工程领域
	 */
	@Schema(description = "投诉类型1工程领域2非工程领域")
	@NotNull
	private Integer complaintType;
	/**
	 * 投诉状态1已投诉2已受理3已完成4已撤诉5驳回
	 */
	@Schema(description = "投诉状态1已投诉2已受理3已完成4已撤诉5驳回")
	private Integer complaintStatus;

	/**
	 * 创建日期
	 */
	private LocalDate createDate;

	/**
	 * 是否现场扫码1现场2非现场
	 */
	private Integer isOnSite;

	/**
	 * 拒绝原因
	 */
	private String remark;

	/**
	 * 投诉顺序
	 */
	private Integer sort;

}
