package org.springblade.modules.biz.utils;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.biz.config.TencentConfig;
import org.springblade.modules.resource.utils.OssUtil;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * OCR识别工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class OcrUtil {

	/**
	 * 调用腾讯云OCR服务识别身份证
	 *
	 * @param images 图片base64字符串
	 * @param config 腾讯云配置
	 * @return OCR识别结果
	 * @throws TencentCloudSDKException 腾讯云SDK异常
	 */
	public static IDCardOCRResponse callTencentOcrService(String images, TencentConfig config) throws TencentCloudSDKException {
		if (Func.isBlank(images)) {
			throw new ServiceException("请检查图片信息是否传入");
		}

		Credential cred = new Credential(config.getSecretId(), config.getSecretKey());
		HttpProfile httpProfile = new HttpProfile();
		httpProfile.setEndpoint("ocr.tencentcloudapi.com");
		ClientProfile clientProfile = new ClientProfile();
		clientProfile.setHttpProfile(httpProfile);
		OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile);
		IDCardOCRRequest req = new IDCardOCRRequest();
		req.setImageBase64(images);
		return client.IDCardOCR(req);
	}

	/**
	 * 保存OCR识别的图片到OSS
	 *
	 * @param images     图片base64字符串
	 * @param fileName   文件名
	 * @param bucketCode OSS存储桶代码
	 * @return 保存的文件信息
	 */
	public static BladeFile saveOcrImageToOss(String images, String fileName, String bucketCode) {
		try (InputStream inputStream = stringToInputStream(images)) {
			BladeFile file = OssUtil.putFile(bucketCode, fileName, inputStream);
			if (Func.isEmpty(file)) {
				throw new ServiceException("保存身份证图片失败");
			}
			return file;
		} catch (IOException e) {
			throw new ServiceException("文件处理失败: " + e.getMessage());
		}
	}

	/**
	 * String转InputStream
	 *
	 * @param text 文本内容
	 * @return InputStream
	 */
	public static InputStream stringToInputStream(String text) {
		return new ByteArrayInputStream(text.getBytes(StandardCharsets.UTF_8));
	}

	/**
	 * 构建OCR文件名
	 *
	 * @param idNum  身份证号
	 * @param prefix 文件名前缀
	 * @return 文件名
	 */
	public static String buildOcrFileName(String idNum, String prefix) {
		if (Func.isBlank(prefix)) {
			prefix = "ocr";
		}
		return prefix + "_" + idNum + ".txt";
	}
}
