package org.springblade.modules.biz.excel;


import lombok.Data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

import java.io.Serializable;
import java.io.Serial;


/**
 * 工资投诉表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class AgentOcrInfoExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * OCR识别的身份证号
	 */
	@ColumnWidth(20)
	@ExcelProperty("OCR识别的身份证号")
	private String ocrIdCard;
	/**
	 * OCR识别的姓名
	 */
	@ColumnWidth(20)
	@ExcelProperty("OCR识别的姓名")
	private String ocrName;
	/**
	 * OCR识别的地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("OCR识别的地址")
	private String ocrAddress;
	/**
	 * OCR识别的民族
	 */
	@ColumnWidth(20)
	@ExcelProperty("OCR识别的民族")
	private String ocrNation;
	/**
	 * 保存的OCR信息
	 */
	@ColumnWidth(20)
	@ExcelProperty("保存的OCR信息")
	private String saveOcrInfo;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}
