package org.springblade.modules.biz.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springblade.modules.biz.pojo.handler.JsonbHandler;
import org.springblade.modules.biz.validation.ValidationGroups;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 工资投诉表-copy Excel实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class WageComplaintExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 投诉渠道
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉渠道")
	private String complaintChannel;


	/**
	 * 被投诉项目名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("被投诉项目名称")
	private String complaintProjectName;
	/**
	 * 被投诉单位名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("被投诉单位名称")
	private String complaintCompanyName;
	/**
	 * 单位地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("单位地址")
	private String companyAddress;
	/**
	 * 建设单位地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("建设单位地址")
	private String constructionCompanyAddress;
	/**
	 * 施工单位名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("施工单位名称")
	private String constructionCompanyName;
	/**
	 * 总承包人
	 */
	@ColumnWidth(20)
	@ExcelProperty("总承包人")
	private String generalContractor;
	/**
	 * 总承包人联系电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("总承包人联系电话")
	private String generalContractorPhone;
	/**
	 * 法人代表
	 */
	@ColumnWidth(20)
	@ExcelProperty("法人代表")
	private String legalRepresentative;
	/**
	 * 法人联系电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("法人联系电话")
	private String legalRepresentativePhone;
	/**
	 * 项目经理/单位负责人
	 */
	@ColumnWidth(30)
	@ExcelProperty("项目经理/单位负责人")
	private String projectManager;
	/**
	 * 项目经理/单位负责人联系电话
	 */
	@ColumnWidth(35)
	@ExcelProperty("项目经理/单位负责人联系电话")
	private String projectManagerPhone;
	/**
	 * 投诉人姓名
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉人姓名")
	private String complaintPersonName;
	/**
	 * 投诉人身份证号
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉人身份证号")
	private String complaintPersonIdCard;

	/**
	 * 投诉人电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉人电话")
	private String complaintPersonPhone;

	/**
	 * 务工所在地
	 */
	@ColumnWidth(20)
	@ExcelProperty("务工所在地")

	private String workLocationS;
	/**
	 * 欠薪时段
	 */
	@ColumnWidth(20)
	@ExcelProperty("欠薪时段")
	private String arrearsPeriodS;
	/**
	 * 工种
	 */
	@ColumnWidth(20)
	@ExcelProperty("工种")
	private String workType;
	/**
	 * 欠薪人数
	 */
	@ColumnWidth(20)
	@ExcelProperty("欠薪人数")
	private Integer arrearsPeopleCount;
	/**
	 * 欠薪金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("欠薪金额")
	private BigDecimal arrearsAmount;
	/**
	 * 投诉内容
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉内容")
	private String complaintContent;

	/**
	 * 投诉类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉类型")
	private String complaintTypeS;

	/**
	 * 创建日期
	 */
	@ExcelProperty("创建日期")
	public LocalDate createDate;

}
