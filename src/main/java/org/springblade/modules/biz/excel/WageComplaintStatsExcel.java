package org.springblade.modules.biz.excel;


import lombok.Data;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

import java.io.Serializable;
import java.io.Serial;


/**
 * 工资投诉表-copy Excel实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class WageComplaintStatsExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键")
	private Long id;
	/**
	 * 投诉的id
	 */
	@ColumnWidth(20)
	@ExcelProperty("投诉的id")
	private Long wageComplainId;
	/**
	 * 顺序
	 */
	@ColumnWidth(20)
	@ExcelProperty("顺序")
	private Integer sort;
	/**
	 * 处理状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("处理状态")
	private Integer complaintStats;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;

}
