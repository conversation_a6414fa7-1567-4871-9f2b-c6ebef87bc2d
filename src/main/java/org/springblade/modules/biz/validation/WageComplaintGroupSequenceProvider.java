package org.springblade.modules.biz.validation;

import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;

import java.util.ArrayList;
import java.util.List;

public class WageComplaintGroupSequenceProvider implements DefaultGroupSequenceProvider<WageComplaintEntity> {
	@Override
	public List<Class<?>> getValidationGroups(WageComplaintEntity entity) {
		List<Class<?>> groups = new ArrayList<>();

		// 默认组总是需要的
		groups.add(WageComplaintEntity.class);

		if (entity != null) {
			// 根据投诉类型选择验证组
			if (Func.isNotEmpty(entity) && Func.isNotEmpty(entity.getComplaintType()) && entity.getComplaintType() == 1) {
				groups.add(ValidationGroups.EngineeringGroup.class);
			} else {
				groups.add(ValidationGroups.NonEngineeringGroup.class);
			}
		}

		return groups;
	}
}
