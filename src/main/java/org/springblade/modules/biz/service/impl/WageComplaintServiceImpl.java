package org.springblade.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintVO;
import org.springblade.modules.biz.mapper.WageComplaintMapper;
import org.springblade.modules.biz.service.IWageComplaintService;
import org.springblade.modules.biz.wrapper.WageComplaintWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工资投诉表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class WageComplaintServiceImpl extends BaseServiceImpl<WageComplaintMapper, WageComplaintEntity> implements IWageComplaintService {

	@Override
	public int getEngineeringComplaintSequence(String idCard) {
		return baseMapper.getEngineeringComplaintCount(idCard) + 1;
	}

	@Override
	public IPage<WageComplaintVO> selectWageComplaintPage(IPage<WageComplaintVO> page, WageComplaintVO wageComplaint) {
		return page.setRecords(baseMapper.selectWageComplaintPage(page, wageComplaint));
	}

	@Override
	public List<WageComplaintVO> getHistoryComplaintList(String idCard) {
		// 查询该用户的所有投诉案件，按创建时间倒序
		List<WageComplaintEntity> complaintList = this.list(Wrappers.lambdaQuery(WageComplaintEntity.class)
			.eq(WageComplaintEntity::getComplaintPersonIdCard, idCard)
			.orderByDesc(WageComplaintEntity::getCreateTime));

		// 使用wrapper转换并设置显示名称
		return complaintList.stream().map(entity -> {
			WageComplaintVO wageComplaintVO = WageComplaintWrapper.build().entityVO(entity);

			// 根据投诉类型设置显示名称
			if (entity.getComplaintType() == 1) {
				// 工程领域，使用sort字段
				wageComplaintVO.setDisplayName("工程建设领域投诉案件" + entity.getSort());
			} else {
				// 非工程领域
				wageComplaintVO.setDisplayName(entity.getComplaintCompanyName());
			}

			return wageComplaintVO;
		}).collect(Collectors.toList());
	}

}
