package org.springblade.modules.biz.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity;
import org.springblade.modules.biz.pojo.vo.AgentOcrInfoVO;
import org.springblade.modules.biz.excel.AgentOcrInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 工资投诉表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface IAgentOcrInfoService extends BaseService<AgentOcrInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page         分页参数
	 * @param agentOcrInfo 查询参数
	 * @return IPage<AgentOcrInfoVO>
	 */
	IPage<AgentOcrInfoVO> selectAgentOcrInfoPage(IPage<AgentOcrInfoVO> page, AgentOcrInfoVO agentOcrInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<AgentOcrInfoExcel>
	 */
	List<AgentOcrInfoExcel> exportAgentOcrInfo(Wrapper<AgentOcrInfoEntity> queryWrapper);

}
