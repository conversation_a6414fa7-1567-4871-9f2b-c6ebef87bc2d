package org.springblade.modules.biz.service.impl;

import org.springblade.modules.biz.pojo.entity.AgentOcrInfoEntity;
import org.springblade.modules.biz.pojo.vo.AgentOcrInfoVO;
import org.springblade.modules.biz.excel.AgentOcrInfoExcel;
import org.springblade.modules.biz.mapper.AgentOcrInfoMapper;
import org.springblade.modules.biz.service.IAgentOcrInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.List;

/**
 * 工资投诉表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class AgentOcrInfoServiceImpl extends BaseServiceImpl<AgentOcrInfoMapper, AgentOcrInfoEntity> implements IAgentOcrInfoService {

	@Override
	public IPage<AgentOcrInfoVO> selectAgentOcrInfoPage(IPage<AgentOcrInfoVO> page, AgentOcrInfoVO agentOcrInfo) {
		return page.setRecords(baseMapper.selectAgentOcrInfoPage(page, agentOcrInfo));
	}


	@Override
	public List<AgentOcrInfoExcel> exportAgentOcrInfo(Wrapper<AgentOcrInfoEntity> queryWrapper) {
		List<AgentOcrInfoExcel> agentOcrInfoList = baseMapper.exportAgentOcrInfo(queryWrapper);
		//agentOcrInfoList.forEach(agentOcrInfo -> {
		//	agentOcrInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, AgentOcrInfo.getType()));
		//});
		return agentOcrInfoList;
	}

}
