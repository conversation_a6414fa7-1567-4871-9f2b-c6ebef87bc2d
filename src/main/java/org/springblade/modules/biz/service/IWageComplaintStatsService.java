package org.springblade.modules.biz.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintStatsVO;
import org.springblade.modules.biz.excel.WageComplaintStatsExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 工资投诉表-copy 服务类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IWageComplaintStatsService extends BaseService<WageComplaintStatsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page               分页参数
	 * @param wageComplaintStats 查询参数
	 * @return IPage<WageComplaintStatsVO>
	 */
	IPage<WageComplaintStatsVO> selectWageComplaintStatsPage(IPage<WageComplaintStatsVO> page, WageComplaintStatsVO wageComplaintStats);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<WageComplaintStatsExcel>
	 */
	List<WageComplaintStatsExcel> exportWageComplaintStats(Wrapper<WageComplaintStatsEntity> queryWrapper);

	/**
	 * 设置处理进度表的状态
	 *
	 * @param wageComplainId 投诉事件id
	 * @param status         状态
	 * @return boolean
	 */
	boolean setWageComplaintStats(Long wageComplainId, Integer status);

}
