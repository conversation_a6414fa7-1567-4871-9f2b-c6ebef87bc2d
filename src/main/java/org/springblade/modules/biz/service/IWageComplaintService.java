package org.springblade.modules.biz.service;

import org.springblade.modules.biz.pojo.entity.WageComplaintEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 工资投诉表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IWageComplaintService extends BaseService<WageComplaintEntity> {

	/**
	 * 获取用户工程建设领域的投诉序号
	 *
	 * @param idCard 用户身份证号
	 * @return 投诉序号
	 */
	int getEngineeringComplaintSequence(String idCard);

	/**
	 * 自定义分页
	 *
	 * @param page          分页参数
	 * @param wageComplaint 查询参数
	 * @return IPage<WageComplaintVO>
	 */
	IPage<WageComplaintVO> selectWageComplaintPage(IPage<WageComplaintVO> page, WageComplaintVO wageComplaint);

	/**
	 * 获取历史投诉案件列表
	 *
	 * @param idCard 用户身份证号
	 * @return 历史投诉案件列表
	 */
	List<WageComplaintVO> getHistoryComplaintList(String idCard);

}
