package org.springblade.modules.biz.service.impl;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.biz.pojo.entity.WageComplaintStatsEntity;
import org.springblade.modules.biz.pojo.vo.WageComplaintStatsVO;
import org.springblade.modules.biz.excel.WageComplaintStatsExcel;
import org.springblade.modules.biz.mapper.WageComplaintStatsMapper;
import org.springblade.modules.biz.service.IWageComplaintStatsService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.List;

/**
 * 工资投诉表-copy 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class WageComplaintStatsServiceImpl extends BaseServiceImpl<WageComplaintStatsMapper, WageComplaintStatsEntity> implements IWageComplaintStatsService {

	@Override
	public IPage<WageComplaintStatsVO> selectWageComplaintStatsPage(IPage<WageComplaintStatsVO> page, WageComplaintStatsVO wageComplaintStats) {
		return page.setRecords(baseMapper.selectWageComplaintStatsPage(page, wageComplaintStats));
	}


	@Override
	public List<WageComplaintStatsExcel> exportWageComplaintStats(Wrapper<WageComplaintStatsEntity> queryWrapper) {
		List<WageComplaintStatsExcel> wageComplaintStatsList = baseMapper.exportWageComplaintStats(queryWrapper);
		//wageComplaintStatsList.forEach(wageComplaintStats -> {
		//	wageComplaintStats.setTypeName(DictCache.getValue(DictEnum.YES_NO, WageComplaintStats.getType()));
		//});
		return wageComplaintStatsList;
	}

	@Override
	public boolean setWageComplaintStats(Long wageComplainId, Integer status) {
		int sort = baseMapper.getMaxSort(wageComplainId);
		WageComplaintStatsEntity wageComplaintStatsEntity = new WageComplaintStatsEntity();
		wageComplaintStatsEntity.setComplaintStats(status);
		wageComplaintStatsEntity.setSort(sort + 1);
		wageComplaintStatsEntity.setWageComplainId(wageComplainId);
		wageComplaintStatsEntity.setProcessorUser(AuthUtil.getUserId());
		return this.save(wageComplaintStatsEntity);
	}

}
