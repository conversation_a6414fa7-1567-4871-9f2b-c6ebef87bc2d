package org.springblade.modules.resource.utils;

import org.springblade.core.oss.OssTemplate;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.resource.builder.OssBuilder;

import java.io.InputStream;

/**
 * 短信服务工具类
 *
 * <AUTHOR>
 */
public class OssUtil {


	private static OssBuilder ossBuilder;

	/**
	 * 获取Oss服务构建类
	 *
	 * @return OssBuilder
	 */
	public static OssBuilder getBuilder() {
		if (ossBuilder == null) {
			ossBuilder = SpringUtil.getBean(OssBuilder.class);
		}
		return ossBuilder;
	}

	/**
	 * 上传文件
	 *
	 * @return 验证码参数
	 */
	public static BladeFile putFile(String code, String fileName, InputStream inputStream) {
		OssTemplate template = getBuilder().template(code);
		return template.putFile(fileName, inputStream);
	}


}
