#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API监控脚本
监控指定接口，当状态码从502变为200时弹出提醒
"""

import requests
import time
import tkinter as tk
from tkinter import messagebox
import threading
import sys
from datetime import datetime

class APIMonitor:
    def __init__(self, url, check_interval=30):
        """
        初始化API监控器
        
        Args:
            url (str): 要监控的API地址
            check_interval (int): 检查间隔时间（秒），默认30秒
        """
        self.url = url
        self.check_interval = check_interval
        self.last_status = None
        self.is_running = False
        
    def check_api_status(self):
        """检查API状态"""
        try:
            response = requests.get(self.url, timeout=10)
            return response.status_code
        except requests.exceptions.RequestException as e:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 请求异常: {e}")
            return None
    
    def show_notification(self, message, title="API监控提醒"):
        """显示桌面通知"""
        # 创建一个隐藏的主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 显示消息框
        messagebox.showinfo(title, message)
        root.destroy()
    
    def play_sound(self):
        """播放提示音（可选）"""
        try:
            # 在macOS上播放系统提示音
            import os
            os.system('afplay /System/Library/Sounds/Glass.aiff')
        except:
            # 如果播放失败，打印到控制台
            print("🔔 提示音播放失败，但接口已恢复正常！")
    
    def monitor_loop(self):
        """监控循环"""
        print(f"开始监控接口: {self.url}")
        print(f"检查间隔: {self.check_interval}秒")
        print("按 Ctrl+C 停止监控\n")
        
        while self.is_running:
            current_status = self.check_api_status()
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if current_status is not None:
                print(f"[{current_time}] 状态码: {current_status}")
                
                # 如果之前是502或其他错误状态，现在变成200，则弹出提醒
                if (self.last_status is not None and 
                    self.last_status != 200 and 
                    current_status == 200):
                    
                    message = f"🎉 接口恢复正常！\n\n接口地址: {self.url}\n状态码: {current_status}\n时间: {current_time}"
                    print(f"\n*** {message} ***\n")
                    
                    # 在新线程中显示通知，避免阻塞监控循环
                    notification_thread = threading.Thread(
                        target=self.show_notification, 
                        args=(message, "接口恢复正常")
                    )
                    notification_thread.daemon = True
                    notification_thread.start()
                    
                    # 播放提示音
                    sound_thread = threading.Thread(target=self.play_sound)
                    sound_thread.daemon = True
                    sound_thread.start()
                
                self.last_status = current_status
            else:
                print(f"[{current_time}] 无法连接到接口")
            
            # 等待指定时间后再次检查
            time.sleep(self.check_interval)
    
    def start(self):
        """开始监控"""
        self.is_running = True
        try:
            self.monitor_loop()
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.stop()
    
    def stop(self):
        """停止监控"""
        self.is_running = False

def main():
    # 要监控的接口地址
    api_url = "https://zkzy.qhdksy.cn/cjgl/loginpage/listLoginPageInfos?pageCode=cjcx"
    
    # 检查间隔（秒）
    check_interval = 30
    
    # 创建监控器实例
    monitor = APIMonitor(api_url, check_interval)
    
    # 开始监控
    monitor.start()

if __name__ == "__main__":
    main()
